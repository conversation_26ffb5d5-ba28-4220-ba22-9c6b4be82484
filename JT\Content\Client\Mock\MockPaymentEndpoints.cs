namespace JT.Content.Client.Mock;

public class MockPaymentEndpoints(ISerializer serializer, ILogger<BaseMockEndpoint> logger) : BaseMockEndpoint(serializer, logger)
{
    public async Task<string> HandlePaymentRequest(HttpRequestMessage request)
    {
        var path = request.RequestUri?.AbsolutePath ?? string.Empty;
        var segments = request.RequestUri?.Segments ?? [];
        var query = request.RequestUri?.Query ?? string.Empty;

        try
        {
            // GET /api/Payment/User/{userId}/Transactions - Get user's payment transactions
            if (path.Contains("/api/Payment/User/") && path.Contains("/Transactions") && request.Method == HttpMethod.Get)
            {
                var userIdSegment = segments[^2]; // Second to last segment
                return await GetUserPayments(userIdSegment, query);
            }

            // GET /api/Payment/Transaction/{paymentId} - Get specific payment transaction
            if (path.Contains("/api/Payment/Transaction/") && request.Method == HttpMethod.Get)
            {
                var paymentId = segments.LastOrDefault()?.TrimEnd('/');
                return await GetPaymentById(paymentId);
            }

            // POST /api/Payment/User/{userId}/Create - Create new payment
            if (path.Contains("/api/Payment/User/") && path.Contains("/Create") && request.Method == HttpMethod.Post)
            {
                var userIdSegment = segments[^2];
                var requestBody = await request.Content?.ReadAsStringAsync() ?? "{}";
                return await CreatePayment(userIdSegment, requestBody);
            }

            // POST /api/Payment/Transaction/{paymentId}/Process - Process payment
            if (path.Contains("/api/Payment/Transaction/") && path.Contains("/Process") && request.Method == HttpMethod.Post)
            {
                var paymentId = segments[^2];
                var requestBody = await request.Content?.ReadAsStringAsync() ?? "{}";
                return await ProcessPayment(paymentId, requestBody);
            }

            // PUT /api/Payment/Transaction/{paymentId}/Status - Update payment status
            if (path.Contains("/api/Payment/Transaction/") && path.Contains("/Status") && request.Method == HttpMethod.Put)
            {
                var paymentId = segments[^2];
                var requestBody = await request.Content?.ReadAsStringAsync() ?? "{}";
                return await UpdatePaymentStatus(paymentId, requestBody);
            }

            return "{}";
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error handling payment request for path: {Path}", path);
            return "{}";
        }
    }

    private async Task<string> GetUserPayments(string userIdSegment, string query)
    {
        if (!Guid.TryParse(userIdSegment, out var userId))
            return serializer.ToString(new { data = new List<PaymentTransactionData>(), total = 0, page = 1, pageSize = 50 });

        var payments = await LoadData<List<PaymentTransactionData>>("PaymentTransactions.json") ?? [];
        var userPayments = payments.Where(p => p.UserId == userId).ToList();

        // Handle pagination
        var page = ExtractIntParameter(query, "page") ?? 1;
        var pageSize = ExtractIntParameter(query, "pageSize") ?? 50;
        var skip = (page - 1) * pageSize;

        // Handle filtering by status
        var status = ExtractParameter(query, "status");
        if (!string.IsNullOrEmpty(status))
        {
            userPayments = userPayments.Where(p => 
                string.Equals(p.Status, status, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        // Sort by creation date (newest first)
        userPayments = userPayments.OrderByDescending(p => p.CreatedAt).ToList();

        var paginatedPayments = userPayments.Skip(skip).Take(pageSize).ToList();

        return serializer.ToString(new
        {
            data = paginatedPayments,
            total = userPayments.Count,
            page,
            pageSize,
            totalPages = (int)Math.Ceiling((double)userPayments.Count / pageSize)
        });
    }

    private async Task<string> GetPaymentById(string? paymentId)
    {
        if (string.IsNullOrEmpty(paymentId))
            return "null";

        var payments = await LoadData<List<PaymentTransactionData>>("PaymentTransactions.json") ?? [];
        var payment = payments.FirstOrDefault(p => string.Equals(p.Id, paymentId, StringComparison.OrdinalIgnoreCase));
        
        return payment != null ? serializer.ToString(payment) : "null";
    }

    private async Task<string> CreatePayment(string userIdSegment, string requestBody)
    {
        if (!Guid.TryParse(userIdSegment, out var userId))
            return serializer.ToString(new { success = false, message = "Invalid user ID" });

        try
        {
            var request = serializer.FromString<CreatePaymentRequest>(requestBody);
            if (request == null || string.IsNullOrEmpty(request.SubscriptionPlanId))
                return serializer.ToString(new { success = false, message = "Invalid request" });

            // Get subscription plan details
            var plans = await LoadData<List<SubscriptionPlanData>>("SubscriptionPlans.json") ?? [];
            var plan = plans.FirstOrDefault(p => string.Equals(p.Id, request.SubscriptionPlanId, StringComparison.OrdinalIgnoreCase));
            
            if (plan == null)
                return serializer.ToString(new { success = false, message = "Subscription plan not found" });

            // Create payment transaction
            var payment = new PaymentTransactionData
            {
                Id = $"pay-{Guid.NewGuid().ToString("N")[..8]}",
                UserId = userId,
                SubscriptionPlanId = request.SubscriptionPlanId,
                Amount = plan.PriceOMR,
                Currency = "OMR",
                PaymentMethod = request.PaymentMethod ?? "thawani",
                PaymentGateway = GetPaymentGateway(request.PaymentMethod ?? "thawani"),
                Status = "pending",
                TransactionId = GenerateTransactionId(request.PaymentMethod ?? "thawani"),
                ServiceFee = plan.PriceOMR * 0.05m, // 5% service fee
                GatewayFee = plan.PriceOMR * 0.02m, // 2% gateway fee
                NetAmount = plan.PriceOMR * 0.93m, // 93% net amount
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddMinutes(30)
            };

            return serializer.ToString(payment);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating payment for user {UserId}", userId);
            return serializer.ToString(new { success = false, message = "Internal error" });
        }
    }

    private async Task<string> ProcessPayment(string paymentId, string requestBody)
    {
        try
        {
            var request = serializer.FromString<ProcessPaymentRequest>(requestBody);
            if (request == null)
                return serializer.ToString(new { success = false, message = "Invalid request" });

            // In a real app, this would process the payment with the gateway
            // For mock, we simulate success/failure based on the payment method
            var success = request.PaymentMethod != "failed_test";

            var payment = new PaymentTransactionData
            {
                Id = paymentId,
                Status = success ? "completed" : "failed",
                CompletedAt = success ? DateTime.UtcNow : null,
                FailedAt = success ? null : DateTime.UtcNow,
                ErrorMessage = success ? null : "Payment failed - insufficient funds",
                GatewayResponse = new
                {
                    payment_id = paymentId,
                    status = success ? "paid" : "failed",
                    gateway_reference = $"gw_{Guid.NewGuid().ToString("N")[..8]}"
                }
            };

            return serializer.ToString(payment);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing payment {PaymentId}", paymentId);
            return serializer.ToString(new { success = false, message = "Internal error" });
        }
    }

    private async Task<string> UpdatePaymentStatus(string paymentId, string requestBody)
    {
        try
        {
            var request = serializer.FromString<UpdatePaymentStatusRequest>(requestBody);
            if (request == null || string.IsNullOrEmpty(request.Status))
                return serializer.ToString(new { success = false, message = "Invalid request" });

            // In a real app, this would update the database
            // For mock, we just return the updated payment
            var payment = new PaymentTransactionData
            {
                Id = paymentId,
                Status = request.Status,
                ErrorMessage = request.ErrorMessage,
                CompletedAt = request.Status == "completed" ? DateTime.UtcNow : null,
                FailedAt = request.Status == "failed" ? DateTime.UtcNow : null
            };

            return serializer.ToString(payment);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating payment status for {PaymentId}", paymentId);
            return serializer.ToString(new { success = false, message = "Internal error" });
        }
    }

    private string GetPaymentGateway(string paymentMethod) => paymentMethod switch
    {
        "thawani" => "Thawani Pay",
        "zaincash" => "ZainCash",
        "card" => "Credit Card Gateway",
        _ => "Unknown Gateway"
    };

    private string GenerateTransactionId(string paymentMethod) => paymentMethod switch
    {
        "thawani" => $"thw_{Guid.NewGuid().ToString("N")[..8]}",
        "zaincash" => $"zc_{Guid.NewGuid().ToString("N")[..8]}",
        "card" => $"cc_{Guid.NewGuid().ToString("N")[..8]}",
        _ => $"unk_{Guid.NewGuid().ToString("N")[..8]}"
    };

    private int? ExtractIntParameter(string query, string paramName)
    {
        var value = ExtractParameter(query, paramName);
        return int.TryParse(value, out var result) ? result : null;
    }

    private string? ExtractParameter(string query, string paramName)
    {
        if (string.IsNullOrEmpty(query)) return null;
        
        var pairs = query.TrimStart('?').Split('&');
        var pair = pairs.FirstOrDefault(p => p.StartsWith($"{paramName}=", StringComparison.OrdinalIgnoreCase));
        return pair?.Split('=')[1];
    }
}

// Request models for payment operations
public class CreatePaymentRequest
{
    public string? SubscriptionPlanId { get; set; }
    public string? PaymentMethod { get; set; }
}

public class ProcessPaymentRequest
{
    public string? PaymentMethod { get; set; }
    public object? GatewayResponse { get; set; }
}

public class UpdatePaymentStatusRequest
{
    public string? Status { get; set; }
    public string? ErrorMessage { get; set; }
}
