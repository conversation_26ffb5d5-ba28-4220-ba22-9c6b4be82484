namespace JT.Business.Services.Sharing;

/// <summary>
/// Implements content sharing related methods
/// </summary>
public interface IShareService
{
    ///<summary>
    /// Open native sharing for a transferRequest and its steps
    /// </summary>
    /// <param name="transferRequest">transferRequest to share</param>
    /// <param name="steps">transferRequest's steps</param>
    /// <param name="ct"></param>
    /// <returns>
    /// </returns>
    Task ShareTransferRequest(TransferRequest transferRequest, IImmutableList<Step> steps, CancellationToken ct);
}
