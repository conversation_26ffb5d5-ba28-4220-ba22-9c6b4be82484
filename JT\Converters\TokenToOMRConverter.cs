using Microsoft.UI.Xaml.Data;

namespace JT.Converters;

public class TokenToOMRConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, string language)
    {
        if (value is int tokens)
        {
            var omrValue = tokens * 0.0001m; // 1 token = 0.1 baisa = 0.0001 OMR
            return $"{omrValue:F3} OMR";
        }
        return "0.000 OMR";
    }

    public object ConvertBack(object value, Type targetType, object parameter, string language)
    {
        throw new NotImplementedException();
    }
}
