namespace JT.Server.Entities;

public class UserData
{
	public Guid Id { get; set; }
    public string? UrlProfileImage { get; set; }
    public string? FirstName { get; set; }
	public string? SecondName { get; set; }
	public string? Tribe { get; set; }
	public string? FullName { get; set; }
	public string? Email { get; set; }
	public string? PhoneNumber { get; set; }
	public string? Password { get; set; }
	public int? Age { get; set; }
	public string? Gender { get; set; }
	public string? CurrentEmployerLocation { get; set; }
	public string? CurrentEmployerCity { get; set; }
	public string? CurrentEmployerState { get; set; }
	public string? CurrentSalaryGrade { get; set; }
	public string? EducationalQualification { get; set; }
	public SubscriptionPlanData? SubscriptionTier { get; set; }
	public int? TokenBalance { get; set; }
	public string? ProfileImageUrl { get; set; }
	public string? Description { get; set; }
	public DateTime? JoinDate { get; set; }
	public bool? IsActive { get; set; }
	public string? ReferralCode { get; set; }
	public int? InvitedFriends { get; set; }
	public int? CompletedTransfers { get; set; }
	public bool IsCurrent { get; set; }

	// Additional fields from reference project
	public string? CurrentEmployer { get; set; }
	public string? CurrentPosition { get; set; }
	public int? YearsOfExperience { get; set; }
	public List<string>? Skills { get; set; }
	public List<string>? Languages { get; set; }
	public List<string>? Certifications { get; set; }
	public string? PreferredWorkType { get; set; } // office, remote, hybrid
	public bool? IsAvailableForTransfer { get; set; }
	public string? LinkedInProfile { get; set; }
	public string? Portfolio { get; set; }
	public string? ResumeUrl { get; set; }
	public string? Bio { get; set; }
	public DateTime? LastLoginAt { get; set; }
	public DateTime? UpdatedAt { get; set; }
}
