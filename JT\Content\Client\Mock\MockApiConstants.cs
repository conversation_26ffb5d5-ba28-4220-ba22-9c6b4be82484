namespace JT.Content.Client.Mock;

internal static class MockApiConstants
{
    // File Names
    internal const string savedTransferRequestsFile = "SavedTransferRequests.json";
    internal const string transferRequestsFile = "TransferRequests.json";
    internal const string categoriesFile = "Categories.json";
    internal const string notificationsFile = "Notifications.json";
    internal const string userFile = "Users.json";
    internal const string jobTransfersFile = "JobTransfers.json";
    internal const string savedJobTransfersFile = "SavedJobTransfers.json";
    internal const string subscriptionPlansFile = "SubscriptionPlans.json";
    internal const string tokenTransactionsFile = "TokenTransactions.json";
    internal const string paymentTransactionsFile = "PaymentTransactions.json";
    internal const string skillsFile = "Skills.json";

    // API Paths
    internal const string transferRequestBasePath = "/api/TransferRequest";
    internal const string categoryPath = "/api/TransferRequest/categories";
    internal const string trendingPath = "/api/TransferRequest/trending";
    internal const string popularPath = "/api/TransferRequest/popular";
    internal const string favoritedPath = "/api/TransferRequest/favorited";
    internal const string stepsPath = "/steps";
    internal const string ingredientsPath = "/ingredients";
    internal const string reviewsPath = "/reviews";
    internal const string reviewLikePath = "/api/TransferRequest/review/like";
    internal const string reviewDislikePath = "/api/TransferRequest/review/dislike";
    internal const string jobTransferControllerPath = "/api/JobTransferController";
    internal const string subscriptionControllerPath = "/api/SubscriptionController";
}
