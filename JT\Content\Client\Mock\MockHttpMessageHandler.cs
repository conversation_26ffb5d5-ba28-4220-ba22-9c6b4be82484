using System.Net;
using System.Text;

namespace JT.Content.Client.Mock;

public class MockHttpMessageHandler : HttpMessageHandler
{
	private readonly string _basePath = Path.Combine(Directory.GetCurrentDirectory(), "Data", "AppData");

	private readonly MockTransferEndpoints _mockTransferEndpoints;
    private readonly MockUserEndpoints _mockUserEndpoints;
    private readonly MockJobTransferEndpoints _mockJobTransferEndpoints;
	private readonly MockNotificationEndpoints _mockNotificationEndpoints;
	private readonly MockSubscriptionEndpoints _mockSubscriptionEndpoints;
	//private readonly MockTokenEndpoints _mockTokenEndpoints;
	//private readonly MockPaymentEndpoints _mockPaymentEndpoints;

	public MockHttpMessageHandler(ISerializer serializer, ILogger<BaseMockEndpoint> logger)
	{
        _mockTransferEndpoints = new MockTransferEndpoints(_basePath, serializer, logger);
        _mockUserEndpoints = new MockUserEndpoints(_basePath, serializer, logger);
		_mockJobTransferEndpoints = new MockJobTransferEndpoints(_basePath, serializer, logger);
		_mockNotificationEndpoints = new MockNotificationEndpoints(_basePath, serializer, logger);
		_mockSubscriptionEndpoints = new MockSubscriptionEndpoints(serializer, logger);
		//_mockTokenEndpoints = new MockTokenEndpoints(serializer, logger);
		//_mockPaymentEndpoints = new MockPaymentEndpoints(serializer, logger);
	}

	protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
	{
		var mockResponse = new HttpResponseMessage(HttpStatusCode.OK)
		{
			Content = new StringContent(await GetMockData(request), Encoding.UTF8, "application/json")
		};

		return await Task.FromResult(mockResponse);
	}

	private async Task<string> GetMockData(HttpRequestMessage request)
	{
		// Handle Transfer Requests
		if (request.RequestUri.AbsolutePath.Contains("/api/TransferRequest"))
		{
			return await _mockTransferEndpoints.HandleTransferRequestsRequest(request);
		}

		// Handle Job Categories
		//if (request.RequestUri.AbsolutePath.Contains("/api/JobCategory"))
		//{
		//	return await _mockTransferEndpoints.HandleTransferRequestsRequest(request);
		//}

		// Handle Users
		if (request.RequestUri.AbsolutePath.Contains("/api/User"))
		{
			return await _mockUserEndpoints.HandleUsersRequest(request);
		}

        // Handle JobTransfers, Skills, SubscriptionPlans, TokenTransactions
        if (request.RequestUri.AbsolutePath.Contains("/api/JobTransfer") || 
            request.RequestUri.AbsolutePath.Contains("/api/Skill") ||
            request.RequestUri.AbsolutePath.Contains("/api/SubscriptionPlan") ||
            request.RequestUri.AbsolutePath.Contains("/api/TokenTransaction"))
        {
            return await _mockJobTransferEndpoints.HandleJobTransferRequest(request);
        }

        // Handle Notifications
        if (request.RequestUri.AbsolutePath.Contains("/api/Notification"))
		{
			return await _mockNotificationEndpoints.HandleNotificationsRequest(request);
		}

		// Handle Subscription endpoints
		if (request.RequestUri.AbsolutePath.Contains("/api/Subscription"))
		{
			return await _mockSubscriptionEndpoints.HandleSubscriptionRequest(request);
		}

		// Handle Token endpoints
		//if (request.RequestUri.AbsolutePath.Contains("/api/Token"))
		//{
		//	return await _mockTokenEndpoints.HandleTokenRequest(request);
		//}

		// Handle Payment endpoints
		//if (request.RequestUri.AbsolutePath.Contains("/api/Payment"))
		//{
		//	return await _mockPaymentEndpoints.HandlePaymentRequest(request);
		//}

		return "{}";
	}
}
