{"descriptionHash": "F45D4D94D89D168A94F1BAA0ECDB7C71CDE17760C774679A12B97AE52E6C88CF07625F656092F18E261B1F25924F5C3D55A97E226528FB8DF7F1513397A348F2", "descriptionLocation": "../../Specs/JTApiClient.swagger.json", "lockFileVersion": "1.0.0", "kiotaVersion": "1.27.0", "clientClassName": "JTApiClient", "typeAccessModifier": "Public", "clientNamespaceName": "JT.Content.Client", "language": "CSharp", "usesBackingStore": false, "excludeBackwardCompatible": false, "includeAdditionalData": true, "disableSSLValidation": false, "serializers": ["Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory", "Microsoft.Kiota.Serialization.Text.TextSerializationWriterFactory", "Microsoft.Kiota.Serialization.Form.FormSerializationWriterFactory", "Microsoft.Kiota.Serialization.Multipart.MultipartSerializationWriterFactory"], "deserializers": ["Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory", "Microsoft.Kiota.Serialization.Text.TextParseNodeFactory", "Microsoft.Kiota.Serialization.Form.FormParseNodeFactory"], "structuredMimeTypes": ["application/json", "text/plain;q=0.9", "application/x-www-form-urlencoded;q=0.2", "multipart/form-data;q=0.1"], "includePatterns": [], "excludePatterns": [], "disabledValidationRules": []}