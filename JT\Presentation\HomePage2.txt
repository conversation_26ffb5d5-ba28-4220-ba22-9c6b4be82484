﻿<Page x:Class="JT.Presentation.HomePage2"
	  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	  xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
	  xmlns:uer="using:Uno.Extensions.Reactive.UI"
	  xmlns:utu="using:Uno.Toolkit.UI"
	  xmlns:uen="using:Uno.Extensions.Navigation.UI"
	  xmlns:ut="using:Uno.Themes"
	  mc:Ignorable="d"
	  utu:StatusBar.Background="{ThemeResource SurfaceInverseBrush}"
	  utu:StatusBar.Foreground="AutoInverse"
	  NavigationCacheMode="Enabled"
	  Background="{ThemeResource BackgroundBrush}">
	<Page.Resources>
        <DataTemplate x:Key="HomeLargeItemTemplate">
            <utu:CardContentControl Margin="0"
									Width="328"
									CornerRadius="4"
									Style="{StaticResource FilledCardContentControlStyle}">
                <utu:AutoLayout Background="{ThemeResource SurfaceBrush}"
								CornerRadius="4"
								PrimaryAxisAlignment="Center"
								HorizontalAlignment="Stretch">
                    <Border Height="144">
                        <Image HorizontalAlignment="Center"
							   VerticalAlignment="Center"
							   Source="{Binding ImageUrl}"
							   Stretch="UniformToFill" />
                    </Border>
                    <utu:AutoLayout Spacing="16"
									Padding="16"
									Justify="SpaceBetween"
									Orientation="Horizontal">
                        <utu:AutoLayout Spacing="4">
                            <TextBlock TextWrapping="Wrap"
									   Text="{Binding Name}"
									   Foreground="{ThemeResource OnSurfaceBrush}"
									   Style="{StaticResource TitleSmall}" />
                            <TextBlock TextWrapping="Wrap"
									   Text="{Binding TimeCal}"
									   Foreground="{ThemeResource OnSurfaceMediumBrush}"
									   Style="{StaticResource CaptionMedium}" />
                        </utu:AutoLayout>
                        <ToggleButton Style="{StaticResource IconToggleButtonStyle}"
									  IsChecked="{Binding IsFavorite}"
									  Command="{utu:AncestorBinding AncestorType=uer:FeedView,
																	Path=DataContext.FavoriteTransfer}"
									  CommandParameter="{Binding}">
                            <ToggleButton.Content>
                                <PathIcon Data="{StaticResource Icon_Heart}"
										  Foreground="{ThemeResource OnSurfaceBrush}" />
                            </ToggleButton.Content>
                            <ut:ControlExtensions.AlternateContent>
                                <PathIcon Data="{StaticResource Icon_Heart_Filled}"
										  Foreground="{ThemeResource PrimaryBrush}" />
                            </ut:ControlExtensions.AlternateContent>
                        </ToggleButton>
                    </utu:AutoLayout>
                </utu:AutoLayout>
            </utu:CardContentControl>
        </DataTemplate>
    </Page.Resources>
	<utu:AutoLayout utu:AutoLayout.PrimaryAlignment="Stretch">
		<utu:NavigationBar x:Name="NavBar"
						   Style="{StaticResource JTsNavigationBarStyle}">
			<utu:NavigationBar.Content>
				<Grid>
					<Image Source="{ThemeResource JTsAppSignature}"
						   HorizontalAlignment="Left"
						   Width="128"
						   Height="40" />
				</Grid>
			</utu:NavigationBar.Content>
			<utu:NavigationBar.PrimaryCommands>
				<AppBarButton uen:Navigation.Request="!Profile">
					<AppBarButton.Icon>
						<PathIcon Data="{StaticResource Icon_Person_Outline}" />
					</AppBarButton.Icon>
				</AppBarButton>
				<AppBarButton uen:Navigation.Request="!Notifications">
					<AppBarButton.Icon>
						<PathIcon Data="{StaticResource Icon_Notification_Bell}" />
					</AppBarButton.Icon>
				</AppBarButton>
			</utu:NavigationBar.PrimaryCommands>
		</utu:NavigationBar>
		<ScrollViewer utu:AutoLayout.PrimaryAlignment="Stretch">
			<utu:AutoLayout Padding="0,0,0,16" Spacing="24">

				<!-- Welcome Section -->
				<uer:FeedView Source="{Binding UserProfile}">
					<DataTemplate>
						<utu:AutoLayout Padding="16,24,16,16" Spacing="8">
							<TextBlock Text="{Binding Data.FullName, Converter={StaticResource StringFormatter}, ConverterParameter='Welcome, {0}'}"
									   Style="{StaticResource TitleLarge}"
									   Foreground="{ThemeResource OnSurfaceBrush}" />
							<TextBlock Text="Explore job transfer opportunities"
									   Style="{StaticResource BodyMedium}"
									   Foreground="{ThemeResource OnSurfaceMediumBrush}" />
						</utu:AutoLayout>
					</DataTemplate>
				</uer:FeedView>

				<!-- Wallet Balance Section -->
				<utu:CardContentControl Margin="16,0" Style="{StaticResource FilledCardContentControlStyle}">
					<utu:AutoLayout Padding="16" Spacing="12" Orientation="Horizontal" Justify="SpaceBetween">
						<utu:AutoLayout Spacing="8" Orientation="Horizontal">
							<Border Background="{ThemeResource PrimaryBrush}"
									CornerRadius="8" Width="40" Height="40">
								<PathIcon Data="{StaticResource Icon_Person}"
										  Foreground="White" Width="20" Height="20"/>
							</Border>
							<utu:AutoLayout Spacing="4">
								<TextBlock Text="Wallet Balance"
										   Style="{StaticResource BodyMedium}"
										   Foreground="{ThemeResource OnSurfaceMediumBrush}" />
								<uer:FeedView Source="{Binding TokenBalanceOMR}">
									<DataTemplate>
										<TextBlock Text="{Binding Data}"
												   Style="{StaticResource TitleMedium}"
												   Foreground="{ThemeResource PrimaryBrush}" />
									</DataTemplate>
								</uer:FeedView>
							</utu:AutoLayout>
						</utu:AutoLayout>
						<Button Content="Share &amp; Earn"
								Style="{StaticResource OutlinedButtonStyle}"
								Command="{Binding ShareAndEarn}" />
					</utu:AutoLayout>
				</utu:CardContentControl>

				<!-- Referral Section -->
				<utu:CardContentControl Margin="16,0"
										Style="{StaticResource FilledCardContentControlStyle}"
										Background="{ThemeResource TertiaryContainerBrush}">
					<uer:FeedView Source="{Binding UserProfile}">
						<DataTemplate>
							<utu:AutoLayout Padding="16" Spacing="12">
								<utu:AutoLayout Spacing="8" Orientation="Horizontal">
									<Border Background="{ThemeResource TertiaryBrush}"
											CornerRadius="20" Width="40" Height="40">
										<PathIcon Data="{StaticResource Icon_Person}"
												  Foreground="White" Width="20" Height="20"/>
									</Border>
									<utu:AutoLayout Spacing="4">
										<TextBlock Text="Invite Friends &amp; Earn"
												   Style="{StaticResource TitleSmall}"
												   Foreground="{ThemeResource OnTertiaryContainerBrush}" />
										<TextBlock Text="Share your referral code and earn 0.200 OMR for each friend who joins"
												   Style="{StaticResource BodySmall}"
												   Foreground="{ThemeResource OnTertiaryContainerMediumBrush}"
												   TextWrapping="Wrap" />
									</utu:AutoLayout>
								</utu:AutoLayout>

								<utu:AutoLayout Spacing="8" Orientation="Horizontal" Justify="SpaceBetween">
									<TextBlock Text="{Binding Data.ReferralCode}"
											   Style="{StaticResource TitleMedium}"
											   Foreground="{ThemeResource OnTertiaryContainerBrush}" />
									<Button Content="Copy"
											Style="{StaticResource TextButtonStyle}"
											Foreground="{ThemeResource TertiaryBrush}"
											Command="{Binding Parent.CopyReferralCode}" />
								</utu:AutoLayout>

								<TextBlock Text="Code expires in: 48:00:00"
										   Style="{StaticResource BodySmall}"
										   Foreground="{ThemeResource OnTertiaryContainerMediumBrush}" />
							</utu:AutoLayout>
						</DataTemplate>
					</uer:FeedView>
				</utu:CardContentControl>
				<!-- Current Subscription Section -->
				<utu:CardContentControl Margin="16,0"
										Style="{StaticResource FilledCardContentControlStyle}"
										Background="{ThemeResource PrimaryBrush}">
					<uer:FeedView Source="{Binding CurrentSubscription}">
						<DataTemplate>
							<utu:AutoLayout Padding="16" Spacing="12">
								<utu:AutoLayout Spacing="8" Orientation="Horizontal" Justify="SpaceBetween">
									<utu:AutoLayout Spacing="4">
										<TextBlock Text="Current Subscription"
												   Style="{StaticResource BodyMedium}"
												   Foreground="{ThemeResource OnPrimaryBrush}" />
										<TextBlock Text="{Binding Data.DisplayName}"
												   Style="{StaticResource TitleLarge}"
												   Foreground="{ThemeResource OnPrimaryBrush}" />
									</utu:AutoLayout>
									<Border Background="{ThemeResource OnPrimaryBrush}"
											CornerRadius="20" Width="40" Height="40">
										<PathIcon Data="{StaticResource Icon_Person}"
												  Foreground="{ThemeResource PrimaryBrush}" Width="20" Height="20"/>
									</Border>
								</utu:AutoLayout>

								<TextBlock Text="Valid until: May 30, 2025"
										   Style="{StaticResource BodyMedium}"
										   Foreground="{ThemeResource OnPrimaryBrush}" />

								<ProgressBar Value="8" Maximum="10"
											 Foreground="{ThemeResource OnPrimaryBrush}"
											 Background="{ThemeResource OnPrimaryMediumBrush}" />

								<TextBlock Text="2 transfer requests remaining"
										   Style="{StaticResource BodySmall}"
										   Foreground="{ThemeResource OnPrimaryBrush}" />
							</utu:AutoLayout>
						</DataTemplate>
					</uer:FeedView>
				</utu:CardContentControl>
				<!-- Quick Actions Section -->
				<utu:AutoLayout Padding="16,0" Spacing="16">
					<TextBlock Text="Quick Actions"
							   Style="{StaticResource TitleMedium}"
							   Foreground="{ThemeResource OnSurfaceBrush}" />

					<utu:AutoLayout Orientation="Horizontal"
									Justify="SpaceEvenly"
									Spacing="24">
						<!-- New Transfer -->
						<utu:AutoLayout Spacing="8" utu:AutoLayout.CounterAlignment="Center">
							<Button Style="{StaticResource FilledButtonStyle}"
									Width="60" Height="60"
									CornerRadius="30"
									Background="{ThemeResource PrimaryContainerBrush}"
									Command="{Binding CreateNewTransfer}">
								<PathIcon Data="{StaticResource Icon_Person}"
										  Foreground="{ThemeResource OnPrimaryContainerBrush}"
										  Width="24" Height="24"/>
							</Button>
							<TextBlock Text="New Transfer"
									   Style="{StaticResource BodySmall}"
									   Foreground="{ThemeResource OnSurfaceBrush}"
									   TextAlignment="Center" />
						</utu:AutoLayout>

						<!-- Browse Jobs -->
						<utu:AutoLayout Spacing="8" utu:AutoLayout.CounterAlignment="Center">
							<Button Style="{StaticResource FilledButtonStyle}"
									Width="60" Height="60"
									CornerRadius="30"
									Background="{ThemeResource SecondaryContainerBrush}"
									Command="{Binding BrowseJobs}">
								<PathIcon Data="{StaticResource Icon_Search}"
										  Foreground="{ThemeResource OnSecondaryContainerBrush}"
										  Width="24" Height="24"/>
							</Button>
							<TextBlock Text="Browse Jobs"
									   Style="{StaticResource BodySmall}"
									   Foreground="{ThemeResource OnSurfaceBrush}"
									   TextAlignment="Center" />
						</utu:AutoLayout>

						<!-- Profile -->
						<utu:AutoLayout Spacing="8" utu:AutoLayout.CounterAlignment="Center">
							<Button Style="{StaticResource FilledButtonStyle}"
									Width="60" Height="60"
									CornerRadius="30"
									Background="{ThemeResource TertiaryContainerBrush}"
									Command="{Binding ViewProfile}">
								<PathIcon Data="{StaticResource Icon_Person}"
										  Foreground="{ThemeResource OnTertiaryContainerBrush}"
										  Width="24" Height="24"/>
							</Button>
							<TextBlock Text="Profile"
									   Style="{StaticResource BodySmall}"
									   Foreground="{ThemeResource OnSurfaceBrush}"
									   TextAlignment="Center" />
						</utu:AutoLayout>
					</utu:AutoLayout>
				</utu:AutoLayout>

			</utu:AutoLayout>
		</ScrollViewer>
	</utu:AutoLayout>
</Page>
