﻿<Project Sdk="Uno.Sdk">
  <PropertyGroup>
    <!-- Building with dotnet build -f net9.0-platform will still cause restore to happen for all TargetFrameworks -->
    <!-- which will force us to install all workloads even if not needed -->
    <!-- To prevent that, we will pass TargetFrameworkOverride as a global property (i.e, dotnet build -p:TargetFrameworkOverride=net9.0-platform) -->
    <!-- That way, we set TargetFrameworks property to only the needed TargetFramework -->
    <TargetFrameworks Condition="'$(TargetFrameworkOverride)'!=''">$(TargetFrameworkOverride)</TargetFrameworks>
    <TargetFrameworks Condition="'$(TargetFrameworkOverride)'==''">
      net9.0-android;
      net9.0-ios;
      net9.0-windows10.0.19041;
      net9.0-desktop;
      net9.0-browserwasm;
      net9.0;
    </TargetFrameworks>

    <OutputType>Exe</OutputType>
    <UnoSingleProject>true</UnoSingleProject>

    <!-- Display name -->
    <ApplicationTitle>JT</ApplicationTitle>
    <!-- App Identifier -->
    <ApplicationId>com.companyname.JT</ApplicationId>
    <ApplicationId Condition="'$(IsCanaryBranch)'=='true'">$(ApplicationId)-canary</ApplicationId>
      <!-- Versions -->
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>
    <UseMocks Condition="'$(UseMocks)'==''">true</UseMocks>
    <!-- Package Publisher -->
    <ApplicationPublisher>MOE</ApplicationPublisher>
    <!-- Package Description -->
    <Description>JT powered by Uno Platform.</Description>
    <!--
      If you encounter this error message:

        error NETSDK1148: A referenced assembly was compiled using a newer version of Microsoft.Windows.SDK.NET.dll.
        Please update to a newer .NET SDK in order to reference this assembly.

      This means that the two packages below must be aligned with the "build" version number of
      the "Microsoft.Windows.SDK.BuildTools" package above, and the "revision" version number
      must be the highest found in https://www.nuget.org/packages/Microsoft.Windows.SDK.NET.Ref.
    -->
    <!-- <WindowsSdkPackageVersion>10.0.22621.28</WindowsSdkPackageVersion> -->

    <!--
      UnoFeatures let's you quickly add and manage implicit package references based on the features you want to use.
      https://aka.platform.uno/singleproject-features
    -->
    <UnoFeatures>
      Material;
      Dsp;
      Hosting;
      Toolkit;
      Logging;
      MVUX;
      Configuration;
      HttpKiota;
      Serialization;
      Localization;
      AuthenticationOidc;
      Navigation;
      Skia;
      ThemeService;
      SkiaRenderer;
    </UnoFeatures>
      <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\JT.DataContracts\JT.DataContracts.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="LiveChartsCore.SkiaSharpView.Uno.WinUI" />
        <PackageReference Include="Mapsui.Uno.WinUI" />
    </ItemGroup>

    <PropertyGroup Condition="'$(UseMocks)'=='true'">
        <DefineConstants>$(DefineConstants);USE_MOCKS</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <EmbeddedResource Include="Assets\Maps\location_pin.svg" />
        <EmbeddedResource Include="Assets\Maps\location_circle.svg" />
    </ItemGroup>

    <PropertyGroup Condition="'$(Configuration)'=='Debug' or '$(IsUiAutomationMappingEnabled)'=='True'">
        <IsUiAutomationMappingEnabled>True</IsUiAutomationMappingEnabled>
        <DefineConstants>$(DefineConstants);USE_UITESTS</DefineConstants>
        <EmbedAssembliesIntoApk Condition="$(IsAndroid)">true</EmbedAssembliesIntoApk>
    </PropertyGroup>

    <PropertyGroup>
        <UnoSplashScreenColor>#313033</UnoSplashScreenColor>
        <UnoIconForegroundScale>0.5</UnoIconForegroundScale>
        <UnoSplashScreenBaseSize>350,300</UnoSplashScreenBaseSize>
        <UnoIconForegroundFile Condition="'$(IsCanaryBranch)'=='true'">Assets/Icons/icon_foreground_canary.svg</UnoIconForegroundFile>
        <UnoSplashScreenFile Condition="'$(IsCanaryBranch)'=='true'">Assets/Splash/splash_screen_canary.svg</UnoSplashScreenFile>
    </PropertyGroup>

    <PropertyGroup Condition="$(IsBrowserWasm) AND '$(Configuration)' == 'Release'">
        <WasmShellMonoRuntimeExecutionMode>InterpreterAndAOT</WasmShellMonoRuntimeExecutionMode>
        <WasmShellEmccLinkOptimization>false</WasmShellEmccLinkOptimization>
        <WasmShellEnableEmccProfiling>true</WasmShellEnableEmccProfiling>
        <UnoXamlResourcesTrimming>true</UnoXamlResourcesTrimming>
    </PropertyGroup>

    <ItemGroup>
        <Content Include="Assets\**\*.png" />
    </ItemGroup>

    <!--<Target Name="AdjustCalabash" BeforeTargets="BeforeBuild">
        --><!-- Needs to be run as a target, as RuntimeIdentifier is set after the csproj is parsed --><!--
        <PropertyGroup Condition="!$(RuntimeIdentifier.Contains('arm64'))">
            <DefineConstants>$(DefineConstants);HAS_TESTCLOUD_AGENT</DefineConstants>
        </PropertyGroup>
    </Target>-->

    <Choose>
        <When Condition="'$(IsCanaryBranch)'=='true'">
            <ItemGroup>
                <UnoImage Include="Assets\Icons\chefslogo_canary.svg" Link="Assets\Icons\chefslogosignature.svg" />
                <UnoImage Include="Assets\Icons\chefslogo_dark_canary.svg" Link="Assets\Icons\chefslogosignature_dark.svg" />
            </ItemGroup>
        </When>
        <Otherwise>
            <ItemGroup>
                <UnoImage Include="Assets\Icons\chefslogo.svg" Link="Assets\Icons\chefslogosignature.svg" />
                <UnoImage Include="Assets\Icons\chefslogo_dark.svg" Link="Assets\Icons\chefslogosignature_dark.svg" />
            </ItemGroup>
        </Otherwise>
    </Choose>

    <Choose>
        <When Condition="$(IsAndroid) or $(IsIOSOrCatalyst)">
            <ItemGroup>
                <IncludeXamlNamespaces Include="mobile" />
                <ExcludeXamlNamespaces Include="not_mobile" />
            </ItemGroup>
        </When>
        <Otherwise>
            <ItemGroup>
                <IncludeXamlNamespaces Include="not_mobile" />
                <ExcludeXamlNamespaces Include="mobile" />
            </ItemGroup>
        </Otherwise>
    </Choose>

    <Choose>
        <When Condition="$(IsIOS)">
            <PropertyGroup Condition="'$(Configuration)'=='Release'">
                <CodesignKey>iPhone Distribution</CodesignKey>

                 <!--BEGIN Workaround - AOT fails currently uno-private #648, not fixed in 9.0.200--> 
                <MtouchUseLlvm>false</MtouchUseLlvm>
                 <!--END Workaround--> 
            </PropertyGroup>
            <!--<ItemGroup Condition="'$(IsUiAutomationMappingEnabled)'=='True' and !$(RuntimeIdentifier.Contains('arm64'))">
                <PackageReference Include="Xamarin.TestCloud.Agent" />
            </ItemGroup>-->
        </When>
        <When Condition="$(IsBrowserWasm)">
            <PropertyGroup Condition="'$(Configuration)'=='Release' and '$(WasmShellMonoRuntimeExecutionMode)'=='InterpreterAndAOT'">
                <WasmAotFileName>aot-skia.profile</WasmAotFileName>
            </PropertyGroup>

            <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
                <WasmShellMonoRuntimeExecutionMode>InterpreterAndAOT</WasmShellMonoRuntimeExecutionMode>
                <WasmShellEmccLinkOptimization>false</WasmShellEmccLinkOptimization>
                <WasmShellEnableEmccProfiling>true</WasmShellEnableEmccProfiling>

                
				<!--Uncomment this block to generate a profile-->
				<WasmShellGenerateAOTProfile>true</WasmShellGenerateAOTProfile>
				<WasmShellILLinkerEnabled>false</WasmShellILLinkerEnabled>
				<UnoXamlResourcesTrimming>false</UnoXamlResourcesTrimming>
				<WasmShellEnableJiterpreter>false</WasmShellEnableJiterpreter>
				
            </PropertyGroup>

            <ItemGroup>
                <EmbeddedResource Include="Platforms\WebAssembly\WasmCSS\**\*.css" />
                <EmbeddedResource Include="Platforms\WebAssembly\WasmScripts\**\*.js" />
                <UpToDateCheckInput Include="Platforms\WebAssembly\WasmCSS\**\*" />
                <UpToDateCheckInput Include="Platforms\WebAssembly\WasmScripts\**\*" />
            </ItemGroup>

            <ItemGroup>
                <None Remove="Platforms\WebAssembly\WasmScripts\TestRunner.js" />
            </ItemGroup>

            <ItemGroup>
                <UpToDateCheckInput Remove="Platforms\WebAssembly\WasmScripts\TestRunner.js" />
            </ItemGroup>
        </When>
    </Choose>

    <!-- https://github.com/dotnet/runtime/issues/109289 -->
    <Target Name="Issue109289_Workaround" AfterTargets="_BrowserWasmWriteRspForLinking" Condition=" $(NETCoreSdkVersion.Contains('9.0.')) ">
        <ItemGroup>
            <_WasmLinkStepArgs Remove="@(_EmccLinkStepArgs)" />
            <_EmccLinkStepArgs Remove="&quot;%(_WasmNativeFileForLinking.Identity)&quot;" />
            <_WasmLinkDependencies Remove="@(_WasmNativeFileForLinking)" />

            <_SkiaSharpToReorder Include="@(_WasmNativeFileForLinking)" Condition="$([System.String]::Copy('%(FullPath)').Contains('SkiaSharp'))" />
            <_WasmNativeFileForLinking Remove="@(_SkiaSharpToReorder)" />
            <_WasmNativeFileForLinking Include="@(_SkiaSharpToReorder)" />

            <_EmccLinkStepArgs Include="&quot;%(_WasmNativeFileForLinking.Identity)&quot;" />
            <_WasmLinkDependencies Include="@(_WasmNativeFileForLinking)" />
            <_WasmLinkStepArgs Include="@(_EmccLinkStepArgs)" />
        </ItemGroup>
    </Target>

    <Target Name="AdjustStrip" BeforeTargets="_WasmCommonPrepareForWasmBuildNative">
        <PropertyGroup>
             <!--Enable debug symbols for release mode to profiling--> 
            <WasmNativeStrip>false</WasmNativeStrip>
            <WasmNativeDebugSymbols>true</WasmNativeDebugSymbols>
        </PropertyGroup>
    </Target>

    <ItemGroup>
        <Content Include="..\AppData\*.json" LinkBase="AppData">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Business\Services\Analytics\**" />
      <Compile Remove="Business\Services\Payments\**" />
      <Compile Remove="Business\Services\Promotions\**" />
      <Compile Remove="Business\Services\Referrals\**" />
      <EmbeddedResource Remove="Business\Services\Analytics\**" />
      <EmbeddedResource Remove="Business\Services\Payments\**" />
      <EmbeddedResource Remove="Business\Services\Promotions\**" />
      <EmbeddedResource Remove="Business\Services\Referrals\**" />
      <None Remove="Business\Services\Analytics\**" />
      <None Remove="Business\Services\Payments\**" />
      <None Remove="Business\Services\Promotions\**" />
      <None Remove="Business\Services\Referrals\**" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Business\Models\Analytics.cs" />
      <Compile Remove="Business\Models\BusinessAdRequest.cs" />
      <Compile Remove="Business\Models\EmployeeProfile.cs" />
      <Compile Remove="Business\Models\Ingredient.cs" />
      <Compile Remove="Business\Models\Organization.cs" />
      <Compile Remove="Business\Models\PaymentTransaction.cs" />
      <Compile Remove="Business\Models\Promotion.cs" />
      <Compile Remove="Business\Models\PromotionAnalytics.cs" />
      <Compile Remove="Business\Models\PromotionPackage.cs" />
      <Compile Remove="Business\Models\Referral.cs" />
      <Compile Remove="Business\Models\SavedSearch.cs" />
      <Compile Remove="Content\Client\Mock\MockPaymentEndpoints.cs" />
      <Compile Remove="Content\Client\Mock\MockTokenEndpoints.cs" />
      <Compile Remove="Presentation\HomePage2.xaml.cs" />
      <Compile Remove="Presentation\PromotionModel.cs" />
      <Compile Remove="Presentation\SubscriptionModel.cs" />
      <Compile Remove="Presentation\TokenModel.cs" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="Presentation\HomePage2.txt" />
      <None Remove="Presentation\HomePage2.xaml.txt" />
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\HomePage2.xaml">
        <SubType>Designer</SubType>
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

</Project>
