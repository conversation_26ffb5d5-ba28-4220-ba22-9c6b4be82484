using System.Net;
using TransferRequestData = JT.Content.Client.Models.TransferRequestData;

namespace JT.Business.Models;

public partial record TransferRequest : IJTEntity
{
	internal TransferRequest(TransferRequestData transferRequestData)
	{
		Id = transferRequestData.Id ?? Guid.Empty;
		UserId = transferRequestData.UserId ?? Guid.Empty;
        ImageUrl = transferRequestData.ImageUrl;
        Name = transferRequestData.Name;
        RequestTitle = transferRequestData.RequestTitle;
		CurrentLocation = new Location(transferRequestData.CurrentLocation);
		DestinationLocation = new Location(transferRequestData.DestinationLocation);
		CurrentSalaryGrade = transferRequestData.CurrentSalaryGrade;
		DesiredSalaryGrade = transferRequestData.DesiredSalaryGrade;
		CurrentFinancialGrade = transferRequestData.CurrentFinancialGrade;
		DesiredFinancialGrade = transferRequestData.DesiredFinancialGrade;
		Industry = transferRequestData.Industry;
        TransferTime = transferRequestData.TransferTime ?? new TimeSpanObject();
        //TransferTime = transferRequestData.TransferTime;
        Category = new Category(transferRequestData.Category);
        Details = transferRequestData.Details;
        TransferReason = transferRequestData.TransferReason;
		Status = transferRequestData.Status;
		Priority = transferRequestData.Priority;
		SubmissionDate = transferRequestData.SubmissionDate?.DateTime ?? DateTime.MinValue;
		ExpirationDate = transferRequestData.ExpirationDate?.DateTime ?? DateTime.MinValue;
		ViewCount = transferRequestData.ViewCount ?? 0;
		InterestedEmployers = transferRequestData.InterestedEmployers ?? 0;
		Documents = transferRequestData.Documents?.ToImmutableList() ?? ImmutableList<string>.Empty;
		RequiredSkills = transferRequestData.RequiredSkills?.ToImmutableList() ?? ImmutableList<string>.Empty;
		PreferredCompanySize = transferRequestData.PreferredCompanySize;
		RemoteWorkPreference = transferRequestData.RemoteWorkPreference;
		LanguageRequirements = transferRequestData.LanguageRequirements?.ToImmutableList() ?? ImmutableList<string>.Empty;
		CreatedBy = new UserProfile(transferRequestData.CreatedBy);
		Responses = transferRequestData.Responses?.Select(r => new EmployerResponse(r)).ToImmutableList() ?? ImmutableList<EmployerResponse>.Empty;
        IsFavorite = transferRequestData.IsFavorite ?? false;
        Nutrition = new Nutrition(transferRequestData?.Nutrition);
        Serves = transferRequestData.Serves ?? 0;
        Difficulty = (Difficulty)(transferRequestData.Difficulty ?? 0);
        Date = transferRequestData.Date ?? DateTime.MinValue;
    }

    public Guid Id { get; init; }
	public Guid UserId { get; init; }
    public string? ImageUrl { get; init; }
    public string? Name { get; init; }
    public string? RequestTitle { get; init; }
	public Location CurrentLocation { get; init; }
	public Location DestinationLocation { get; init; }
	public string? CurrentSalaryGrade { get; init; }
	public string? DesiredSalaryGrade { get; init; }
	public string? CurrentFinancialGrade { get; init; }
	public string? DesiredFinancialGrade { get; init; }
	public string? Industry { get; init; }
    public TimeSpanObject TransferTime { get; init; }
    public Category Category { get; init; }
    public string? Details { get; init; }
    public string? TransferReason { get; init; }
	public string? Status { get; init; }
	public string? Priority { get; init; }
	public DateTime SubmissionDate { get; init; }
	public DateTime ExpirationDate { get; init; }
	public int ViewCount { get; init; }
	public int InterestedEmployers { get; init; }
	public IImmutableList<string> Documents { get; init; }
	public IImmutableList<string> RequiredSkills { get; init; }
	public string? PreferredCompanySize { get; init; }
	public string? RemoteWorkPreference { get; init; }
	public IImmutableList<string> LanguageRequirements { get; init; }
	public UserProfile CreatedBy { get; init; }
	public IImmutableList<EmployerResponse> Responses { get; init; }
    public bool IsFavorite { get; init; }
    public Nutrition Nutrition { get; init; }
    public Difficulty Difficulty { get; init; }
    public int Serves { get; init; }
    public DateTimeOffset Date { get; init; }

    // Computed properties
    public string StatusColor => Status?.ToLowerInvariant() switch
	{
		"pending" => "#FFA500",
		"under review" => "#0066CC",
		"approved" => "#28A745",
		"rejected" => "#DC3545",
		_ => "#6C757D"
	};

	public string PriorityColor => Priority?.ToLowerInvariant() switch
	{
		"high" => "#DC3545",
		"medium" => "#FFA500",
		"low" => "#28A745",
		_ => "#6C757D"
	};

	public bool IsExpired => DateTime.UtcNow > ExpirationDate;
	public bool IsActive => Status?.ToLowerInvariant() is "pending" or "under review";
	public int DaysUntilExpiration => Math.Max(0, (ExpirationDate - DateTime.UtcNow).Days);

	internal TransferRequestData ToData() => new()
	{
		Id = Id,
		UserId = UserId,
        ImageUrl = ImageUrl,
        Name = Name,
        RequestTitle = RequestTitle,
		CurrentLocation = CurrentLocation.ToData(),
		DestinationLocation = DestinationLocation.ToData(),
		CurrentSalaryGrade = CurrentSalaryGrade,
		DesiredSalaryGrade = DesiredSalaryGrade,
		CurrentFinancialGrade = CurrentFinancialGrade,
		DesiredFinancialGrade = DesiredFinancialGrade,
		Industry = Industry,
        TransferTime = TransferTime,
		TransferReason = TransferReason,
		Status = Status,
		Priority = Priority,
		SubmissionDate = SubmissionDate,
		ExpirationDate = ExpirationDate,
		ViewCount = ViewCount,
		InterestedEmployers = InterestedEmployers,
		Documents = Documents.ToList(),
		RequiredSkills = RequiredSkills.ToList(),
		PreferredCompanySize = PreferredCompanySize,
		RemoteWorkPreference = RemoteWorkPreference,
		LanguageRequirements = LanguageRequirements.ToList(),
		CreatedBy = CreatedBy.ToData(),
		Responses = Responses.Select(r => r.ToData()).ToList(),

        Difficulty = (int)Difficulty,
        Serves = Serves,
        Category = Category.ToData(),
        Details = Details,
        Date = Date
    };
}

// Supporting models
public partial record Location
{
	internal Location(LocationData? locationData)
	{
		City = locationData?.City;
		State = locationData?.State;
		Country = locationData?.Country;
	}

	public string? City { get; init; }
	public string? State { get; init; }
	public string? Country { get; init; }

	internal LocationData ToData() => new()
	{
		City = City,
		State = State,
		Country = Country
	};
}

public partial record UserProfile
{
	internal UserProfile(UserProfileData? userProfileData)
	{
		Id = userProfileData?.Id ?? Guid.Empty;
		FullName = userProfileData?.FullName;
		ProfileImageUrl = userProfileData?.ProfileImageUrl;
		CurrentPosition = userProfileData?.CurrentPosition;
		Experience = userProfileData?.Experience;
	}

	public Guid Id { get; init; }
	public string? FullName { get; init; }
	public string? ProfileImageUrl { get; init; }
	public string? CurrentPosition { get; init; }
	public string? Experience { get; init; }

	internal UserProfileData ToData() => new()
	{
		Id = Id,
		FullName = FullName,
		ProfileImageUrl = ProfileImageUrl,
		CurrentPosition = CurrentPosition,
		Experience = Experience
	};
}

public partial record EmployerResponse
{
	internal EmployerResponse(EmployerResponseData? employerResponseData)
	{
		Id = employerResponseData?.Id;
		EmployerId = employerResponseData?.EmployerId;
		CompanyName = employerResponseData?.CompanyName;
		ResponseDate = employerResponseData?.ResponseDate?.DateTime ?? DateTime.MinValue;
		Status = employerResponseData?.Status;
		Message = employerResponseData?.Message;
		OfferedSalary = employerResponseData?.OfferedSalary;
		InterviewScheduled = employerResponseData?.InterviewScheduled ?? false;
	}

	public string? Id { get; init; }
	public string? EmployerId { get; init; }
	public string? CompanyName { get; init; }
	public DateTime ResponseDate { get; init; }
	public string? Status { get; init; }
	public string? Message { get; init; }
	public string? OfferedSalary { get; init; }
	public bool InterviewScheduled { get; init; }
    //public string TimeCal
    //{
    //    get
    //    {
    //        var timeSpan = ToTimeSpan(TransferTime);
    //        return timeSpan > TimeSpan.FromHours(1)
    //            ? $"{timeSpan:%h} hour {timeSpan:%m} mins • {1}"
    //            : $"{timeSpan:%m} mins • {1}";
    //    }
    //}
    internal EmployerResponseData ToData() => new()
	{
		Id = Id,
		EmployerId = EmployerId,
		CompanyName = CompanyName,
		ResponseDate = ResponseDate,
		Status = Status,
		Message = Message,
		OfferedSalary = OfferedSalary,
		InterviewScheduled = InterviewScheduled
	};

    private static TimeSpan ToTimeSpan(TimeSpanObject timeSpanObject)
    {
        return new TimeSpan(timeSpanObject?.Ticks ?? 0);
    }
}
