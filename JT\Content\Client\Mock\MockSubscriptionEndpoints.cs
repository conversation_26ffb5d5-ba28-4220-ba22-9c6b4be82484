namespace JT.Content.Client.Mock;

public class MockSubscriptionEndpoints(ISerializer serializer, ILogger<BaseMockEndpoint> logger) : BaseMockEndpoint(serializer, logger)
{
    public async Task<string> HandleSubscriptionRequest(HttpRequestMessage request)
    {
        var path = request.RequestUri?.AbsolutePath ?? string.Empty;
        var segments = request.RequestUri?.Segments ?? [];

        try
        {
            // GET /api/Subscription/Plans - Get all subscription plans
            if (path.Contains("/api/Subscription/Plans") && request.Method == HttpMethod.Get)
            {
                return await GetAllPlans();
            }

            // GET /api/Subscription/Plans/{planId} - Get specific plan
            if (path.Contains("/api/Subscription/Plans/") && request.Method == HttpMethod.Get)
            {
                var planId = segments.LastOrDefault()?.TrimEnd('/');
                return await GetPlanById(planId);
            }

            // GET /api/Subscription/User/{userId}/Current - Get user's current subscription
            if (path.Contains("/api/Subscription/User/") && path.Contains("/Current") && request.Method == HttpMethod.Get)
            {
                var userIdSegment = segments[^2]; // Second to last segment
                return await GetUserCurrentSubscription(userIdSegment);
            }

            // POST /api/Subscription/User/{userId}/Upgrade - Upgrade user subscription
            if (path.Contains("/api/Subscription/User/") && path.Contains("/Upgrade") && request.Method == HttpMethod.Post)
            {
                var userIdSegment = segments[^2];
                var requestBody = await request.Content?.ReadAsStringAsync() ?? "{}";
                return await UpgradeUserSubscription(userIdSegment, requestBody);
            }

            // GET /api/Subscription/User/{userId}/Features/{feature} - Check feature availability
            if (path.Contains("/api/Subscription/User/") && path.Contains("/Features/") && request.Method == HttpMethod.Get)
            {
                var userIdSegment = segments[^3];
                var feature = segments.LastOrDefault()?.TrimEnd('/');
                return await CheckFeatureAvailability(userIdSegment, feature);
            }

            return "{}";
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error handling subscription request for path: {Path}", path);
            return "{}";
        }
    }

    private async Task<string> GetAllPlans()
    {
        var plans = await LoadData<List<SubscriptionPlanData>>("SubscriptionPlans.json") ?? [];
        return serializer.ToString(plans);
    }

    private async Task<string> GetPlanById(string? planId)
    {
        if (string.IsNullOrEmpty(planId))
            return "null";

        var plans = await LoadData<List<SubscriptionPlanData>>("SubscriptionPlans.json") ?? [];
        var plan = plans.FirstOrDefault(p => string.Equals(p.Id, planId, StringComparison.OrdinalIgnoreCase));
        
        return plan != null ? serializer.ToString([plan]) : "[]";
    }

    private async Task<string> GetUserCurrentSubscription(string userIdSegment)
    {
        if (!Guid.TryParse(userIdSegment, out var userId))
            return "null";

        var users = await LoadData<List<UserData>>("Users.json") ?? [];
        var user = users.FirstOrDefault(u => u.Id == userId);
        
        if (user?.SubscriptionTier == null)
            return "null";

        var plans = await LoadData<List<SubscriptionPlanData>>("SubscriptionPlans.json") ?? [];
        var plan = plans.FirstOrDefault(p => string.Equals(p.Id, user.SubscriptionTier, StringComparison.OrdinalIgnoreCase));
        
        return plan != null ? serializer.ToString(plan) : "null";
    }

    private async Task<string> UpgradeUserSubscription(string userIdSegment, string requestBody)
    {
        if (!Guid.TryParse(userIdSegment, out var userId))
            return serializer.ToString(new { success = false, message = "Invalid user ID" });

        try
        {
            // Parse the upgrade request (expecting { "planId": "gold" })
            var upgradeRequest = serializer.FromString<Dictionary<string, object>>(requestBody);
            var newPlanId = upgradeRequest?.GetValueOrDefault("planId")?.ToString();

            if (string.IsNullOrEmpty(newPlanId))
                return serializer.ToString(new { success = false, message = "Plan ID is required" });

            // Verify plan exists
            var plans = await LoadData<List<SubscriptionPlanData>>("SubscriptionPlans.json") ?? [];
            var newPlan = plans.FirstOrDefault(p => string.Equals(p.Id, newPlanId, StringComparison.OrdinalIgnoreCase));
            
            if (newPlan == null)
                return serializer.ToString(new { success = false, message = "Plan not found" });

            // Update user's subscription (in a real app, this would update the database)
            var users = await LoadData<List<UserData>>("Users.json") ?? [];
            var user = users.FirstOrDefault(u => u.Id == userId);
            
            if (user == null)
                return serializer.ToString(new { success = false, message = "User not found" });

            // In mock, we just return success without actually updating the file
            return serializer.ToString(new { 
                success = true, 
                message = "Subscription upgraded successfully",
                newPlan = newPlan.DisplayName,
                effectiveDate = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ")
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error upgrading subscription for user {UserId}", userId);
            return serializer.ToString(new { success = false, message = "Internal error" });
        }
    }

    private async Task<string> CheckFeatureAvailability(string userIdSegment, string? feature)
    {
        if (!Guid.TryParse(userIdSegment, out var userId) || string.IsNullOrEmpty(feature))
            return serializer.ToString(new { available = false });

        var users = await LoadData<List<UserData>>("Users.json") ?? [];
        var user = users.FirstOrDefault(u => u.Id == userId);
        
        if (user?.SubscriptionTier == null)
            return serializer.ToString(new { available = false });

        var plans = await LoadData<List<SubscriptionPlanData>>("SubscriptionPlans.json") ?? [];
        var plan = plans.FirstOrDefault(p => string.Equals(p.Id, user.SubscriptionTier, StringComparison.OrdinalIgnoreCase));
        
        if (plan == null)
            return serializer.ToString(new { available = false });

        var available = feature.ToLower() switch
        {
            "whatsapp_alerts" => plan.WhatsAppAlerts,
            "sms_alerts" => plan.SMSAlerts,
            "priority_listing" => plan.PriorityListing,
            "ad_free" => plan.AdFreeExperience,
            _ => false
        };

        return serializer.ToString(new { available });
    }
}
