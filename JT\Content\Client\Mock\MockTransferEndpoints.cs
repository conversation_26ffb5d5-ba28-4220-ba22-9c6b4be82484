namespace JT.Content.Client.Mock;


public class MockTransferEndpoints(string basePath, ISerializer serializer, ILogger<BaseMockEndpoint> logger) : BaseMockEndpoint(serializer, logger)
{
    public async Task<string> HandleTransferRequestsRequest(HttpRequestMessage request)
    {
        var savedList = await LoadData<List<Guid>>(MockApiConstants.savedTransferRequestsFile) ?? [];
        var allTransferRequests = await LoadData<List<TransferRequestData>>(MockApiConstants.transferRequestsFile) ?? [];

        allTransferRequests.ForEach((_, r) => r.IsFavorite = savedList.Contains(r.Id ?? Guid.Empty));

        return await RouteRequest(request, allTransferRequests);
    }

    private async Task<string> RouteRequest(HttpRequestMessage request, List<TransferRequestData> allTransferRequests)
    {
        var path = request.RequestUri?.AbsolutePath ?? string.Empty;
        var segments = request.RequestUri?.Segments;

        if (path.Contains(MockApiConstants.categoryPath)) return await HandleCategoriesRequest();
        if (path.Contains(MockApiConstants.trendingPath)) return serializer.ToString(allTransferRequests.Take(10));
        if (path.Contains(MockApiConstants.popularPath)) return serializer.ToString(allTransferRequests.Take(10));
        if (path.Contains(MockApiConstants.favoritedPath)) return serializer.ToString(allTransferRequests.Where(r => r.IsFavorite ?? false).ToList());

        var segmentId = segments?.Length >= 2 ? segments[^2] : string.Empty;
        if (path.Contains(MockApiConstants.stepsPath)) return GetTransferRequestSteps(allTransferRequests, segmentId);
        if (path.Contains(MockApiConstants.ingredientsPath)) return GetTransferRequestIngredients(allTransferRequests, segmentId);
        if (path.Contains(MockApiConstants.reviewsPath)) return GetTransferRequestReviews(allTransferRequests, segmentId);

        if (request.Method == HttpMethod.Get && path == MockApiConstants.transferRequestBasePath) return serializer.ToString(allTransferRequests);

        if (path.Contains(MockApiConstants.reviewLikePath) || path.Contains(MockApiConstants.reviewDislikePath)) return "{}";

        var lastSegment = segments?.LastOrDefault() ?? string.Empty;
        return GetTransferRequestDetails(allTransferRequests, lastSegment);
    }

    private string GetTransferRequestDetails(List<TransferRequestData> allTransferRequests, string transferRequestId)
    {
        transferRequestId = transferRequestId.TrimEnd('/');
        if (Guid.TryParse(transferRequestId, out var gid))
        {
            var transfer = allTransferRequests.FirstOrDefault(x => x.Id == gid);
            if (transfer != null)
            {
                return serializer.ToString(transfer);
            }
        }

        return "{}";
    }

    private async Task<string> HandleCategoriesRequest()
    {
        var allCategories = await LoadData<List<CategoryData>>(MockApiConstants.categoriesFile) ?? new List<CategoryData>();
        return serializer.ToString(allCategories);
    }

    private string GetTransferRequestSteps(List<TransferRequestData> allTransfers, string transferRequestId)
    {
        transferRequestId = transferRequestId.TrimEnd('/');

        if (Guid.TryParse(transferRequestId, out var parsedId))
        {
            var transferRequest = allTransfers.FirstOrDefault(r => r.Id == parsedId);
            if (transferRequest != null && transferRequest.Steps != null)
            {
                return serializer.ToString(transferRequest.Steps);
            }
        }

        return "[]";
    }
    private string GetTransferRequestIngredients(List<TransferRequestData> allTransferRequests, string transferRequestId)
    {
        // Transfer requests don't have ingredients - return empty array
        _ = allTransferRequests; // Suppress unused parameter warning
        _ = transferRequestId; // Suppress unused parameter warning
        return "[]";
    }

    private string GetTransferRequestReviews(List<TransferRequestData> allTransferRequests, string transferRequestId)
    {
        transferRequestId = transferRequestId.TrimEnd('/');

        if (Guid.TryParse(transferRequestId, out var parsedId))
        {
            var transferRequest = allTransferRequests.FirstOrDefault(r => r.Id == parsedId);
            if (transferRequest != null && transferRequest.Responses != null)
            {
                // Return employer responses as "reviews" for transfer requests
                return serializer.ToString(transferRequest.Responses);
            }
        }

        return "[]";
    }


}
