namespace JT.Content.Client.Mock;

public class MockTokenEndpoints(ISerializer serializer, ILogger<BaseMockEndpoint> logger) : BaseMockEndpoint(serializer, logger)
{
    public async Task<string> HandleTokenRequest(HttpRequestMessage request)
    {
        var path = request.RequestUri?.AbsolutePath ?? string.Empty;
        var segments = request.RequestUri?.Segments ?? [];
        var query = request.RequestUri?.Query ?? string.Empty;

        try
        {
            // GET /api/Token/User/{userId}/Transactions - Get user's token transactions
            if (path.Contains("/api/Token/User/") && path.Contains("/Transactions") && request.Method == HttpMethod.Get)
            {
                var userIdSegment = segments[^2]; // Second to last segment
                return await GetUserTransactions(userIdSegment, query);
            }

            // GET /api/Token/User/{userId}/Balance - Get user's token balance
            if (path.Contains("/api/Token/User/") && path.Contains("/Balance") && request.Method == HttpMethod.Get)
            {
                var userIdSegment = segments[^2];
                return await GetUserBalance(userIdSegment);
            }

            // POST /api/Token/User/{userId}/Add - Add tokens to user
            if (path.Contains("/api/Token/User/") && path.Contains("/Add") && request.Method == HttpMethod.Post)
            {
                var userIdSegment = segments[^2];
                var requestBody = await request.Content?.ReadAsStringAsync() ?? "{}";
                return await AddTokens(userIdSegment, requestBody);
            }

            // POST /api/Token/User/{userId}/Deduct - Deduct tokens from user
            if (path.Contains("/api/Token/User/") && path.Contains("/Deduct") && request.Method == HttpMethod.Post)
            {
                var userIdSegment = segments[^2];
                var requestBody = await request.Content?.ReadAsStringAsync() ?? "{}";
                return await DeductTokens(userIdSegment, requestBody);
            }

            // GET /api/Token/Transactions/Type/{type} - Get transactions by type
            if (path.Contains("/api/Token/Transactions/Type/") && request.Method == HttpMethod.Get)
            {
                var type = segments.LastOrDefault()?.TrimEnd('/');
                return await GetTransactionsByType(type, query);
            }

            return "{}";
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error handling token request for path: {Path}", path);
            return "{}";
        }
    }

    private async Task<string> GetUserTransactions(string userIdSegment, string query)
    {
        if (!Guid.TryParse(userIdSegment, out var userId))
            return serializer.ToString(new { data = new List<TokenTransactionData>(), total = 0, page = 1, pageSize = 50 });

        var transactions = await LoadData<List<TokenTransactionData>>("TokenTransactions.json") ?? [];
        var userTransactions = transactions.Where(t => t.UserId == userId).ToList();

        // Handle pagination
        var page = ExtractIntParameter(query, "page") ?? 1;
        var pageSize = ExtractIntParameter(query, "pageSize") ?? 50;
        var skip = (page - 1) * pageSize;

        // Handle filtering by type
        var type = ExtractParameter(query, "type");
        if (!string.IsNullOrEmpty(type))
        {
            userTransactions = userTransactions.Where(t => 
                string.Equals(t.Type, type, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        // Sort by creation date (newest first)
        userTransactions = userTransactions.OrderByDescending(t => t.CreatedAt).ToList();

        var paginatedTransactions = userTransactions.Skip(skip).Take(pageSize).ToList();

        return serializer.ToString(new
        {
            data = paginatedTransactions,
            total = userTransactions.Count,
            page,
            pageSize,
            totalPages = (int)Math.Ceiling((double)userTransactions.Count / pageSize)
        });
    }

    private async Task<string> GetUserBalance(string userIdSegment)
    {
        if (!Guid.TryParse(userIdSegment, out var userId))
            return serializer.ToString(new { balance = 0 });

        var users = await LoadData<List<UserData>>("Users.json") ?? [];
        var user = users.FirstOrDefault(u => u.Id == userId);
        
        var balance = user?.TokenBalance ?? 0;
        return serializer.ToString(new { balance });
    }

    private async Task<string> AddTokens(string userIdSegment, string requestBody)
    {
        if (!Guid.TryParse(userIdSegment, out var userId))
            return serializer.ToString(new { success = false, message = "Invalid user ID" });

        try
        {
            var request = serializer.FromString<AddTokensRequest>(requestBody);
            if (request == null || request.Amount <= 0)
                return serializer.ToString(new { success = false, message = "Invalid request" });

            // Get current balance
            var users = await LoadData<List<UserData>>("Users.json") ?? [];
            var user = users.FirstOrDefault(u => u.Id == userId);
            var currentBalance = user?.TokenBalance ?? 0;
            var newBalance = currentBalance + request.Amount;

            // Create transaction record
            var transaction = new TokenTransactionData
            {
                Id = Guid.NewGuid().ToString(),
                UserId = userId,
                Amount = request.Amount,
                Type = request.Type ?? "manual",
                Description = request.Description ?? "Manual token addition",
                ReferenceId = request.ReferenceId,
                CreatedAt = DateTime.UtcNow,
                BalanceAfter = newBalance
            };

            // In a real app, we would save this to the database
            // For mock, we just return the transaction
            return serializer.ToString(transaction);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error adding tokens for user {UserId}", userId);
            return serializer.ToString(new { success = false, message = "Internal error" });
        }
    }

    private async Task<string> DeductTokens(string userIdSegment, string requestBody)
    {
        if (!Guid.TryParse(userIdSegment, out var userId))
            return serializer.ToString(new { success = false, message = "Invalid user ID" });

        try
        {
            var request = serializer.FromString<DeductTokensRequest>(requestBody);
            if (request == null || request.Amount <= 0)
                return serializer.ToString(new { success = false, message = "Invalid request" });

            // Get current balance
            var users = await LoadData<List<UserData>>("Users.json") ?? [];
            var user = users.FirstOrDefault(u => u.Id == userId);
            var currentBalance = user?.TokenBalance ?? 0;

            if (currentBalance < request.Amount)
                return serializer.ToString(new { success = false, message = "Insufficient balance" });

            var newBalance = currentBalance - request.Amount;

            // Create transaction record
            var transaction = new TokenTransactionData
            {
                Id = Guid.NewGuid().ToString(),
                UserId = userId,
                Amount = -request.Amount, // Negative for deduction
                Type = request.Type ?? "redemption",
                Description = request.Description ?? "Token redemption",
                ReferenceId = request.ReferenceId,
                CreatedAt = DateTime.UtcNow,
                BalanceAfter = newBalance
            };

            return serializer.ToString(transaction);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error deducting tokens for user {UserId}", userId);
            return serializer.ToString(new { success = false, message = "Internal error" });
        }
    }

    private async Task<string> GetTransactionsByType(string? type, string query)
    {
        if (string.IsNullOrEmpty(type))
            return serializer.ToString(new { data = new List<TokenTransactionData>(), total = 0 });

        var transactions = await LoadData<List<TokenTransactionData>>("TokenTransactions.json") ?? [];
        var filteredTransactions = transactions.Where(t => 
            string.Equals(t.Type, type, StringComparison.OrdinalIgnoreCase)).ToList();

        // Handle pagination
        var page = ExtractIntParameter(query, "page") ?? 1;
        var pageSize = ExtractIntParameter(query, "pageSize") ?? 50;
        var skip = (page - 1) * pageSize;

        filteredTransactions = filteredTransactions.OrderByDescending(t => t.CreatedAt).ToList();
        var paginatedTransactions = filteredTransactions.Skip(skip).Take(pageSize).ToList();

        return serializer.ToString(new
        {
            data = paginatedTransactions,
            total = filteredTransactions.Count,
            page,
            pageSize,
            totalPages = (int)Math.Ceiling((double)filteredTransactions.Count / pageSize)
        });
    }

    private int? ExtractIntParameter(string query, string paramName)
    {
        var value = ExtractParameter(query, paramName);
        return int.TryParse(value, out var result) ? result : null;
    }

    private string? ExtractParameter(string query, string paramName)
    {
        if (string.IsNullOrEmpty(query)) return null;
        
        var pairs = query.TrimStart('?').Split('&');
        var pair = pairs.FirstOrDefault(p => p.StartsWith($"{paramName}=", StringComparison.OrdinalIgnoreCase));
        return pair?.Split('=')[1];
    }
}

// Request models for token operations
public class AddTokensRequest
{
    public int Amount { get; set; }
    public string? Type { get; set; }
    public string? Description { get; set; }
    public string? ReferenceId { get; set; }
}

public class DeductTokensRequest
{
    public int Amount { get; set; }
    public string? Type { get; set; }
    public string? Description { get; set; }
    public string? ReferenceId { get; set; }
}
