using CommunityToolkit.Mvvm.Messaging;
//using JT.Business.Services.Subscriptions;
//using JT.Business.Services.Tokens;
using JT.Business.Services.TransferRequests;
using JT.Business.Services.Users;

namespace JT.Presentation;

public partial record HomeModel
{
	private readonly INavigator _navigator;
    private readonly ITransferRequestService _transferRequestService;
    private readonly IUserService _userService;
    //private readonly ISubscriptionService _subscriptionService;
    //private readonly ITokenService _tokenService;
    private readonly IMessenger _messenger;

    public HomeModel(
		INavigator navigator,
        ITransferRequestService transferRequestService,
        IUserService userService,
        //ISubscriptionService subscriptionService,
        ITokenService tokenService,
        IMessenger messenger)
	{
		_navigator = navigator;
        _transferRequestService = transferRequestService;
        _userService = userService;
        //_subscriptionService = subscriptionService;
        //_tokenService = tokenService;
        _messenger = messenger;
    }

    public IListState<TransferRequest> TrendingNow => ListState
    .Async(this, _transferRequestService.GetTrending)
    .Observe(_messenger, r => r.Id);

    public IListFeed<CategoryWithCount> Categories => ListFeed.Async(_transferRequestService.GetCategoriesWithCount);

    public IListFeed<TransferRequest> RecentlyAdded => ListFeed.Async(_transferRequestService.GetRecent);

    public IListFeed<User> PopularCreators => ListFeed.Async(_userService.GetPopularCreators);

    public IFeed<User> UserProfile => _userService.User;

    // New properties for wallet and subscription data
    ////public IState<int> TokenBalance => State.Async(this, GetTokenBalance);
    ////public IState<SubscriptionPlan?> CurrentSubscription => State.Async(this, GetCurrentSubscription);
    ////public IState<string> TokenBalanceOMR => State.Async(this, GetTokenBalanceOMR);

    public async ValueTask ShowAll(CancellationToken ct) =>
        await _navigator.NavigateRouteAsync(this, route: "/Main/-/Search", data: new SearchFilter(FilterGroup: FilterGroup.Popular), cancellation: ct);

    public async ValueTask ShowAllRecentlyAdded(CancellationToken ct) =>
        await _navigator.NavigateRouteAsync(this, route: "/Main/-/Search", data: new SearchFilter(FilterGroup: FilterGroup.Recent), cancellation: ct);

    public async ValueTask CategorySearch(CategoryWithCount categoryWithCount, CancellationToken ct) =>
        await _navigator.NavigateRouteAsync(this, route: "/Main/-/Search", data: new SearchFilter(Category: categoryWithCount.Category), cancellation: ct);

    public async ValueTask FavoriteTransfer(TransferRequest transferRequest, CancellationToken ct) =>
        await _transferRequestService.Favorite(transferRequest, ct);

    // Private methods for data retrieval
    ////private async ValueTask<int> GetTokenBalance(CancellationToken ct)
    ////{
    ////    var user = await _userService.GetCurrent(ct);
    ////    return user?.TokenBalance ?? 0;
    ////}

    ////private async ValueTask<SubscriptionPlan?> GetCurrentSubscription(CancellationToken ct)
    ////{
    ////    var user = await _userService.GetCurrent(ct);
    ////    if (user?.SubscriptionTier != null)
    ////    {
    ////        return await _subscriptionService.GetPlanById(user.SubscriptionTier.ToLower(), ct);
    ////    }
    ////    return null;
    ////}

    ////private async ValueTask<string> GetTokenBalanceOMR(CancellationToken ct)
    ////{
    ////    var balance = await GetTokenBalance(ct);
    ////    var omrValue = balance * 0.0001m; // 1 token = 0.1 baisa = 0.0001 OMR
    ////    return $"{omrValue:F3} OMR";
    ////}

    //public IListFeed<TransferRequest> TrendingTransfers => ListFeed.Async(_transferRequestService.GetTrending);

    //public IListFeed<CategoryWithCount> Categories => ListFeed.Async(_transferRequestService.GetCategoriesWithCount);

    //   public IListFeed<TransferRequest> RecentTransfers => ListFeed.Async(_transferRequestService.GetAll);

    //   public IListFeed<User> ActiveUsers => ListFeed.Async(_userService.GetPopularCreators);

    //public IFeed<User> UserProfile => _userService.User;

    //   public IFeed<SubscriptionPlan> CurrentSubscription => Feed.Async(async ct =>
    //   {
    //       var subscription = await GetCurrentSubscription(ct);
    //       return subscription ?? new SubscriptionPlan(new SubscriptionPlanData { Id = "free", Name = "Free Plan" });
    //   });

    //   public IState<int> TokenBalance => State.Async(this, GetTokenBalance);

    //   private async ValueTask<SubscriptionPlan?> GetCurrentSubscription(CancellationToken ct)
    //   {
    //       var user = await _userService.GetCurrent(ct);
    //       if (user?.SubscriptionTier != null)
    //       {
    //           return await _subscriptionService.GetPlanById(user.SubscriptionTier.Id?.ToLowerInvariant() ?? "", ct);
    //       }
    //       return null;
    //   }

    //   private async ValueTask<int> GetTokenBalance(CancellationToken ct)
    //{
    //	var user = await _userService.GetCurrent(ct);
    //	return user?.TokenBalance ?? 0;
    //}

    // Navigation methods
    //    public async ValueTask ShowAllTransfers(CancellationToken ct) =>
    //        await _navigator.NavigateRouteAsync(this, route: "/Main/-/Search", cancellation: ct);

    //    public async ValueTask ShowRecentTransfers(CancellationToken ct) =>
    //        await _navigator.NavigateRouteAsync(this, route: "/Main/-/Recent", cancellation: ct);

    //    public async ValueTask CategorySearch(Category Category, CancellationToken ct) =>
    //        await _navigator.NavigateRouteAsync(this, route: "/Main/-/Search", data: new { Category = Category }, cancellation: ct);

    //    public async ValueTask ViewTransferRequest(TransferRequest transferRequest, CancellationToken ct)
    //    {
    //        await _transferRequestService.IncrementViewCount(transferRequest.Id, ct);
    //        await _navigator.NavigateRouteAsync(this, route: $"/Main/-/TransferRequest/{transferRequest.Id}", cancellation: ct);
    //    }

    //    public async ValueTask CreateTransferRequest(CancellationToken ct) =>
    //        await _navigator.NavigateRouteAsync(this, route: "/Main/-/CreateTransfer", cancellation: ct);

    //    public async ValueTask ViewSubscriptions(CancellationToken ct) =>
    //        await _navigator.NavigateRouteAsync(this, route: "/Main/-/Subscriptions", cancellation: ct);

    //    public async ValueTask ViewTokens(CancellationToken ct) =>
    //        await _navigator.NavigateRouteAsync(this, route: "/Main/-/Tokens", cancellation: ct);

    // Navigation methods for Quick Actions
    ////public async ValueTask CreateNewTransfer(CancellationToken ct) =>
    ////    await _navigator.NavigateRouteAsync(this, route: "/Main/-/CreateTransfer", cancellation: ct);

    ////public async ValueTask BrowseJobs(CancellationToken ct) =>
    ////    await _navigator.NavigateRouteAsync(this, route: "/Main/-/Search", cancellation: ct);

    ////public async ValueTask ViewProfile(CancellationToken ct) =>
    ////    await _navigator.NavigateRouteAsync(this, route: "/Main/-/Profile", cancellation: ct);

    ////public async ValueTask ShareAndEarn(CancellationToken ct) =>
    ////    await _navigator.NavigateRouteAsync(this, route: "/Main/-/Tokens", cancellation: ct);

    ////public async ValueTask CopyReferralCode(CancellationToken ct)
    ////{
    ////    var user = await _userService.GetCurrent(ct);
    ////    if (user?.ReferralCode != null)
    ////    {
    ////        // Copy to clipboard logic would go here
    ////        //await _messenger.Send(new InfoMessage($"Referral code {user.ReferralCode} copied!"));
    ////    }
    ////}
}
